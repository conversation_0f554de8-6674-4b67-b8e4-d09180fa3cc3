# PDF缓存混乱问题修复

## 问题描述

在数据库清空后重启前后端服务时，用户浏览器缓存的PDF会出现在合同详情页面中，导致合同详情显示的PDF不是对应合同的PDF，而是其他缓存中的PDF。这是一个严重的数据一致性问题。

## 问题根因分析

1. **缓存键冲突**：
   - 前端使用URL作为缓存键：`/api/files/${contractId}/preview`
   - 数据库清空后，新合同的ID从1开始重复使用
   - 导致新合同使用了旧合同的缓存数据

2. **ETag生成不唯一**：
   - 后端ETag仅基于文件大小和修改时间
   - 不同文件可能有相同的大小和修改时间，导致ETag冲突

3. **缓存策略过于激进**：
   - 30天长期缓存策略
   - 数据库清空后，浏览器仍然使用旧缓存

## 解决方案

### 1. 后端修复

#### 1.1 增强ETag唯一性
```javascript
// 生成唯一的ETag，包含合同ID确保唯一性
const uniqueETag = `"contract-${contract.id}-${contract.file_path}-${stats.size}-${stats.modified ? stats.modified.getTime() : Date.now()}"`;
```

#### 1.2 添加自定义响应头
```javascript
// 添加合同ID到响应头，用于前端缓存标识
res.setHeader('X-Contract-ID', contract.id.toString());
res.setHeader('X-File-Path', contract.file_path);
```

#### 1.3 优化缓存策略
```javascript
// 缩短缓存时间，增强验证
res.setHeader('Cache-Control', 'public, max-age=86400, must-revalidate'); // 1天缓存
res.setHeader('Vary', 'Authorization, X-Contract-ID'); // 基于认证和合同ID变化
```

#### 1.4 支持HEAD请求
- 添加HEAD请求支持，用于获取文件元数据
- 前端可以通过HEAD请求验证缓存有效性

### 2. 前端修复

#### 2.1 增强缓存键生成
```javascript
generateCacheKey(url, metadata = {}) {
  // 提取合同ID
  const contractIdMatch = url.match(/\/files\/(\d+)\/preview/);
  const contractId = contractIdMatch ? contractIdMatch[1] : null;
  
  // 使用合同ID、文件路径和ETag生成唯一键
  const keyParts = [
    url,
    contractId ? `contract-${contractId}` : '',
    metadata.etag || '',
    metadata.filePath || '',
  ].filter(Boolean);
  
  return keyParts.join('|');
}
```

#### 2.2 缓存验证机制
```javascript
// 验证缓存是否仍然有效（检查ETag）
if (metadata.etag && cacheEntry.metadata.etag && 
    metadata.etag !== cacheEntry.metadata.etag) {
  // ETag不匹配，缓存已过期
  await this.removeCachedPDF(cacheKey);
  return null;
}
```

#### 2.3 自动缓存同步
- 实现`CacheSync`类，在应用启动时检测数据库重置
- 通过合同数量变化检测数据库重置
- 自动清理过期缓存

### 3. 新增功能

#### 3.1 缓存管理工具
- 开发环境下的缓存管理界面
- 支持查看缓存状态、清理缓存等操作
- 路径：`/dev/cache-manager`（仅开发环境）

#### 3.2 缓存测试页面
- 用于测试缓存修复效果的专用页面
- 路径：`/dev/cache-test`（仅开发环境）

#### 3.3 统计API
- 新增`GET /api/contracts/stats`接口
- 用于获取合同统计信息，支持缓存同步检测

## 修改的文件

### 后端文件
- `backend/src/routes/files.js` - 修复PDF预览路由，增强ETag和缓存策略
- `backend/src/routes/contracts.js` - 添加统计信息API

### 前端文件
- `frontend/src/utils/pdfCacheManager.js` - 增强缓存键生成和验证
- `frontend/src/services/pdfCacheService.js` - 支持HEAD请求和元数据验证
- `frontend/src/utils/cacheSync.js` - 新增缓存同步工具
- `frontend/src/main.js` - 在应用启动时初始化缓存同步
- `frontend/src/components/dev/CacheManager.vue` - 新增缓存管理工具
- `frontend/src/views/CacheTestPage.vue` - 新增缓存测试页面
- `frontend/src/router/index.js` - 添加开发工具路由

## 测试步骤

1. **准备测试环境**：
   - 启动前后端服务
   - 登录系统并上传几个PDF合同

2. **验证正常功能**：
   - 查看合同详情页面，确认PDF显示正确
   - 检查浏览器开发者工具中的缓存情况

3. **模拟问题场景**：
   - 清空数据库
   - 重启前后端服务
   - 上传新的PDF文件

4. **验证修复效果**：
   - 查看新合同的PDF是否显示正确
   - 确认不会显示旧的缓存PDF
   - 检查缓存键是否唯一

5. **使用开发工具**：
   - 访问`/dev/cache-manager`查看缓存状态
   - 访问`/dev/cache-test`进行专项测试

## 预期效果

1. **唯一性保证**：每个PDF文件都有唯一的缓存标识
2. **自动清理**：数据库重置后自动清理过期缓存
3. **验证机制**：通过ETag验证缓存有效性
4. **开发工具**：提供便捷的缓存管理和测试工具

## 注意事项

1. 此修复向后兼容，不会影响现有功能
2. 开发工具仅在开发环境可用，生产环境不会暴露
3. 缓存策略调整为1天，平衡性能和数据一致性
4. 建议在生产环境部署前进行充分测试

## 监控建议

1. 监控PDF加载失败率
2. 监控缓存命中率变化
3. 监控用户反馈的PDF显示问题
4. 定期检查缓存大小和清理效果
