<template>
  <div class="contract-number-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><EditPen /></el-icon>
            合同编号管理
          </h1>
          <p class="page-description">为通过审核的合同分配唯一的合同编号</p>
        </div>
        <div class="stats-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.pending || 0 }}</div>
                  <div class="stat-label">待分配编号</div>
                </div>
                <el-icon class="stat-icon pending"><Timer /></el-icon>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.completed || 0 }}</div>
                  <div class="stat-label">已完成</div>
                </div>
                <el-icon class="stat-icon completed"><CircleCheckFilled /></el-icon>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-number">{{ stats.total || 0 }}</div>
                  <div class="stat-label">总计</div>
                </div>
                <el-icon class="stat-icon total"><DataBoard /></el-icon>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <el-card class="list-card">
        <template #header>
          <div class="card-header">
            <span>待分配编号的合同</span>
            <el-button 
              type="primary" 
              :icon="Refresh" 
              @click="loadContracts"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </template>

        <!-- 合同列表 -->
        <el-table
          v-loading="loading"
          :data="contracts"
          stripe
          @row-click="handleRowClick"
          style="width: 100%"
        >
          <el-table-column prop="serial_number" label="流水号" width="120" />
          <el-table-column prop="filename" label="合同文件" min-width="200">
            <template #default="{ row }">
              <div class="filename-cell">
                <el-icon><Document /></el-icon>
                <span>{{ row.filename }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="submitter_name" label="提交人" width="120" />
          <el-table-column prop="reviewed_at" label="审核完成时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.reviewed_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">
                {{ formatStatus(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click.stop="assignContractNumber(row)"
              >
                分配编号
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 分配合同编号对话框 -->
    <ContractNumberAssignDialog
      v-model="showAssignDialog"
      :contract="currentContract"
      @assigned="handleContractNumberAssigned"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useTabs } from "@/composables/useTabs";
import {
  EditPen,
  Timer,
  CircleCheckFilled,
  DataBoard,
  Refresh,
  Document,
} from "@element-plus/icons-vue";

import ContractNumberAssignDialog from "@/components/contract/ContractNumberAssignDialog.vue";
import { contractsAPI } from "@/api/contracts";
import { formatDateTime } from "@/utils/dateTime";
import { STATUS_LABELS, STATUS_COLORS } from "@/utils/contractStatus";

// Tab管理
const { openTab } = useTabs();

// 响应式数据
const loading = ref(false);
const contracts = ref([]);
const stats = reactive({
  pending: 0,
  completed: 0,
  total: 0,
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 对话框状态
const showAssignDialog = ref(false);
const currentContract = ref(null);

// 加载合同列表
const loadContracts = async () => {
  try {
    loading.value = true;
    const response = await contractsAPI.getPendingContractNumber({
      page: pagination.page,
      pageSize: pagination.pageSize,
    });

    if (response.success) {
      contracts.value = response.data.contracts;
      pagination.total = response.data.total;
      
      // 更新统计数据
      stats.pending = response.data.total;
      stats.total = response.data.total;
    }
  } catch (error) {
    console.error("加载合同列表失败:", error);
    ElMessage.error("加载合同列表失败");
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里可以调用专门的统计API
    // 暂时使用合同列表的数据
  } catch (error) {
    console.error("加载统计数据失败:", error);
  }
};

// 分配合同编号
const assignContractNumber = (contract) => {
  currentContract.value = contract;
  showAssignDialog.value = true;
};

// 处理合同编号分配完成
const handleContractNumberAssigned = async () => {
  try {
    // 强制刷新数据，确保界面显示最新状态
    await Promise.all([loadContracts(), loadStats()]);
  } catch (error) {
    console.error("刷新数据失败:", error);
    // 静默处理刷新失败，不影响用户体验
  }
};

// 处理行点击 - 使用Tab系统打开合同详情
const handleRowClick = (row) => {
  openTab({
    key: `contract-detail-${row.id}`,
    title: `合同详情 - ${row.serial_number}`,
    component: "ContractDetailTab",
    icon: "DocumentCopy",
    params: {
      contractId: row.id,
      contract: row,
    },
  });
};

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val;
  pagination.page = 1;
  loadContracts();
};

const handleCurrentChange = (val) => {
  pagination.page = val;
  loadContracts();
};

// 格式化状态
const formatStatus = (status) => {
  return STATUS_LABELS[status] || status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  return STATUS_COLORS[status] || "info";
};

// 初始化
onMounted(() => {
  loadContracts();
  loadStats();
});
</script>

<style scoped>
.contract-number-management-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section {
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-section {
  margin-top: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  opacity: 0.3;
}

.stat-icon.pending {
  color: #e6a23c;
}

.stat-icon.completed {
  color: #67c23a;
}

.stat-icon.total {
  color: #409eff;
}

.page-content {
  margin-top: 20px;
}

.list-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
